# Spring Boot Gateway 代理模块开发指南

## 项目概述

创建一个基于Maven和Spring Boot 2.7.7的简单代理网关模块，用于在前端和后端之间进行API请求转发。该网关仅作为代理层，不添加任何额外功能如认证、缓存、限流等。

## 技术要求

### 基础框架
- **构建工具**: Maven
- **Spring Boot版本**: 2.7.7 (org.springframework.boot)
- **Java版本**: 8或11
- **项目类型**: Spring Boot应用

### 核心依赖
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
```

## 项目结构

```
gateway/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── gateway/
│       │               ├── GatewayApplication.java
│       │               ├── controller/
│       │               │   └── ProxyController.java
│       │               └── service/
│       │                   └── ProxyService.java
│       └── resources/
│           ├── application.yml
│           └── application-dev.yml
```

## 功能需求

### 1. 代理转发规则

**前端请求路径**: `http://gateway:8080/d/**`
**后端目标地址**: `http://backend-server/d/**`

### 2. 支持的HTTP方法
- GET
- POST
- PUT
- DELETE
- PATCH

### 3. 请求转发要求

#### 请求头转发
- 完整转发所有请求头到后端
- 保持Content-Type不变
- 转发Authorization头（如果存在）
- 转发Cookie（如果存在）

#### 请求体转发
- JSON格式：直接转发
- Form-urlencoded格式：直接转发
- 文件上传：支持multipart/form-data转发

#### 响应转发
- 完整转发后端响应状态码
- 转发所有响应头
- 转发响应体内容
- 保持响应格式不变

### 4. 错误处理
- 后端服务不可用时返回502 Bad Gateway
- 请求超时返回504 Gateway Timeout
- 其他网络错误返回相应HTTP状态码

## API映射表

基于提供的前端和后端API列表，需要支持以下路径的代理转发：

### 认证模块
- `POST /d/login` → 后端 `POST /d/login`
- `POST /d/logout` → 后端 `GET /d/logout`
- `POST /d/auth` → 后端 `POST /d/auth`

### 事件模块
- `POST /d/event` → 后端 `GET /d/event`
- `POST /d/event_feature` → 后端 `GET /d/event_feature`
- `POST /d/evidence` → 后端 `GET /d/evidence`

### 资产模块
- `POST /d/asset` → 后端 `GET /d/feature` (根据type参数)
- `POST /d/statinfo` → 后端相应接口

### 特征模块
- `POST /d/feature` → 后端 `GET /d/feature`

### TopN模块
- `POST /d/topn` → 后端 `GET /d/topn`

### 配置模块
- `POST /d/config` → 后端 `GET /d/config` 或 `POST /d/config`

### 工具API模块
- `POST /d/geoinfo` → 后端 `GET /d/geoinfo`
- `POST /d/portinfo` → 后端 `GET /d/portinfo`
- `POST /d/ipinfo` → 后端 `GET /d/ipinfo`
- `POST /d/threatinfo` → 后端 `GET /d/threatinfo`
- `POST /d/threatinfopro` → 后端 `GET /d/threatinfopro`

### 系统控制模块
- `POST /d/sctl` → 后端 `GET /d/sctl`

## 配置要求

### application.yml
```yaml
server:
  port: 8080

gateway:
  backend:
    base-url: http://localhost  # 后端服务地址
    timeout: 30000  # 请求超时时间(毫秒)
    
logging:
  level:
    com.example.gateway: DEBUG
```

### application-dev.yml
```yaml
gateway:
  backend:
    base-url: http://localhost  # 开发环境后端地址
```

## 实现要点

### 1. 请求方法转换
- 前端统一使用POST请求
- 根据API映射表转换为后端对应的HTTP方法
- 保持请求参数和请求体的完整性

### 2. 参数处理
- JSON请求体转换为查询参数（当后端需要GET请求时）
- Form-urlencoded参数直接转发
- 查询参数保持不变

### 3. 响应处理
- 保持后端响应的原始格式
- 不修改响应内容
- 正确转发HTTP状态码

### 4. 异常处理
- 网络连接异常
- 后端服务超时
- 后端服务返回错误状态码

## 代码实现建议

### ProxyController
- 使用`@RestController`注解
- 使用`@RequestMapping("/d/**")`捕获所有/d路径请求
- 使用`HttpServletRequest`获取完整请求信息

### ProxyService
- 使用`WebClient`进行HTTP请求转发
- 实现请求方法转换逻辑
- 处理请求头和响应头转发

## 测试要求

### 单元测试
- 测试各种HTTP方法的转发
- 测试请求头转发
- 测试错误处理

### 集成测试
- 与实际后端服务集成测试
- 验证所有API路径的正确转发
- 验证响应格式的完整性

## 部署说明

### 打包命令
```bash
mvn clean package
```

### 运行命令
```bash
java -jar gateway-1.0.0.jar --spring.profiles.active=dev
```

### Docker支持（可选）
提供Dockerfile用于容器化部署

## 注意事项

1. **保持简单**: 仅实现代理转发功能，不添加任何业务逻辑
2. **性能考虑**: 使用异步HTTP客户端提高并发性能
3. **错误透传**: 将后端错误完整传递给前端
4. **日志记录**: 记录请求转发的关键信息用于调试
5. **配置灵活**: 后端服务地址可通过配置文件修改

## 验收标准

1. 所有前端API请求能正确转发到后端
2. 请求和响应格式保持完整
3. 错误处理机制正常工作
4. 性能满足基本要求（响应时间<100ms额外延迟）
5. 代码结构清晰，易于维护
