openapi: 3.0.3
info:
  title: 流量分析系统 API
  description: |
    基于C++开发的网络流量分析系统，提供网络安全监控、流量分析、威胁检测等功能。

    ## 系统架构
    - **服务器端**: C++ CGI程序，部署在 `/Server/www/d/` 目录
    - **数据库**: MySQL数据库
    - **协议**: Protocol Buffers数据序列化
    - **认证**: 基于Session的用户认证机制

  version: 1.0.0
  contact:
    name: API Support
  license:
    name: MIT
servers:
  - url: http://localhost/d
    description: 开发环境
  - url: https://api.example.com/d
    description: 生产环境

security:
  - SessionAuth: []

paths:
  /auth:
    post:
      tags:
        - 认证管理
      summary: 用户认证
      description: 用户登录认证和权限验证
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                auth_target:
                  type: string
                  description: 目标API名称
                  enum: [mo, feature, topn, login, logout, event, config, bwlist, internalip, ipinfo, portinfo, locinfo, threatinfo, threatinfopro, geoinfo, auth_status, sctl, event_feature, evidence]
                username:
                  type: string
                  description: 用户名
                password:
                  type: string
                  description: 密码
              required:
                - auth_target
      responses:
        '200':
          description: 认证成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '403':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /login:
    post:
      tags:
        - 认证管理
      summary: 用户登录
      description: 用户登录获取session
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                username:
                  type: string
                  description: 用户名
                password:
                  type: string
                  description: 密码
              required:
                - username
                - password
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: 登录失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /logout:
    get:
      tags:
        - 认证管理
      summary: 用户登出
      description: 用户登出，清除session
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /auth_status:
    get:
      tags:
        - 认证管理
      summary: 认证状态查询
      description: 查询当前用户认证状态
      responses:
        '200':
          description: 状态查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthStatusResponse'

  /feature:
    get:
      tags:
        - 流量分析
      summary: 流量特征提取
      description: 提取网络流量特征数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: type
          in: query
          schema:
            type: string
            enum: [SUS, POP, PORT_SCAN, SERVICE, TCPINIT, FORCE, DNS_TUN, FLOOD, BLACK, WHITE, ASSET_IP, MO, DNS, ASSET_URL, ASSET_HOST, ASSET_SRV, URL_CONTENT, DGA, IP_SCAN, API]
          description: 特征类型
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数限制
        - name: dbg
          in: query
          schema:
            type: integer
            enum: [0, 1]
          description: 调试模式
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FeatureRecord'
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /event_feature:
    get:
      tags:
        - 流量分析
      summary: 事件特征提取
      description: 提取安全事件特征数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: type
          in: query
          schema:
            type: string
            enum: [TI, IP_SCAN, PORT_SCAN, SRV, DNS_TUN, BLACK, MO, DNS, DGA, ICMP_TUN, FRN_TRIP, CAP, URL_CONTENT, DNSTUN_AI, MINING]
          description: 事件类型
        - name: domain
          in: query
          schema:
            type: string
          description: 域名
        - name: url
          in: query
          schema:
            type: string
          description: URL地址
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数限制
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventFeatureRecord'

  /mo:
    get:
      tags:
        - 监控管理
      summary: 查询监控对象
      description: 查询网络监控对象信息
      parameters:
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [GET, GET_FILTER]
          description: 操作类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: moid
          in: query
          schema:
            type: string
          description: 监控对象ID列表
        - name: mogid
          in: query
          schema:
            type: integer
          description: 监控对象组ID
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MonitorObject'
    post:
      tags:
        - 监控管理
      summary: 管理监控对象
      description: 添加、修改、删除监控对象
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                op:
                  type: string
                  enum: [ADD, DEL, MOD, GADD, GDEL, GMOD]
                  description: 操作类型
                moip:
                  type: string
                  description: 监控IP地址
                moport:
                  type: string
                  description: 监控端口
                protocol:
                  type: string
                  description: 协议类型
                pip:
                  type: string
                  description: 对端IP地址
                pport:
                  type: string
                  description: 对端端口
                desc:
                  type: string
                  description: 描述信息
                tag:
                  type: string
                  description: 标签
                moid:
                  type: string
                  description: 监控对象ID
                mogid:
                  type: integer
                  description: 监控对象组ID
                devid:
                  type: integer
                  description: 设备ID
                direction:
                  type: string
                  description: 方向
                filter:
                  type: string
                  description: 过滤条件
              required:
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /event:
    get:
      tags:
        - 事件管理
      summary: 安全事件查询
      description: 查询安全事件数据
      parameters:
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: step
          in: query
          schema:
            type: integer
          description: 时间步长
        - name: type
          in: query
          schema:
            type: string
          description: 事件类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: event_id
          in: query
          schema:
            type: integer
          description: 事件ID
        - name: id
          in: query
          schema:
            type: integer
          description: 记录ID
        - name: obj
          in: query
          schema:
            type: string
          description: 事件对象
        - name: level
          in: query
          schema:
            type: string
          description: 事件级别
        - name: req_type
          in: query
          schema:
            type: string
            enum: [ORI, AGGRE, SET_PROC_STATUS]
          description: 请求类型
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventRecord'

  /sctl:
    get:
      tags:
        - 系统控制
      summary: 系统控制
      description: 系统服务控制和状态查询
      parameters:
        - name: nodetype
          in: query
          required: true
          schema:
            type: string
            enum: [ALL, SERVER, AGENT, PROBE]
          description: 节点类型
        - name: servicetype
          in: query
          required: true
          schema:
            type: string
            enum: [BASIC, SSH, HTTP, DISK]
          description: 服务类型
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [STATUS, START, STOP, RESTART]
          description: 操作类型
        - name: id
          in: query
          schema:
            type: string
          description: 节点ID
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemControlRecord'

  /config:
    get:
      tags:
        - 配置管理
      summary: 查询系统配置
      description: 查询系统配置信息
      parameters:
        - name: type
          in: query
          required: true
          schema:
            type: string
            enum: [event, mo, internalip, internalsrv, agent, bwlist, user]
          description: 配置类型
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [get]
          description: 操作类型
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigResponse'
    post:
      tags:
        - 配置管理
      summary: 管理系统配置
      description: 添加、修改、删除系统配置
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                type:
                  type: string
                  enum: [event, mo, internalip, internalsrv, agent, bwlist, user]
                  description: 配置类型
                op:
                  type: string
                  enum: [add, mod, del]
                  description: 操作类型
              required:
                - type
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /bwlist:
    get:
      tags:
        - 配置管理
      summary: 黑白名单管理
      description: 查询黑白名单配置
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BWListResponse'

  /locinfo:
    get:
      tags:
        - 地理位置
      summary: IP位置信息
      description: 查询IP地址的地理位置信息
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表，逗号分隔
          example: "*******,*******"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LocationInfo'

  /geoinfo:
    get:
      tags:
        - 地理位置
      summary: 地理信息查询
      description: 查询IP地址的详细地理信息（支持中英文）
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表，逗号分隔
          example: "*******,*******"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GeoInfo'

  /threatinfo:
    get:
      tags:
        - 威胁情报
      summary: 威胁情报查询
      description: 查询威胁情报信息
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreatInfo'
        '403':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /threatinfopro:
    get:
      tags:
        - 威胁情报
      summary: 威胁情报专业版
      description: 专业版威胁情报查询
      security:
        - TokenAuth: []
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreatInfoPro'

  /ipinfo:
    get:
      tags:
        - 网络信息
      summary: IP信息查询
      description: 查询IP相关信息
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IPInfo'

  /portinfo:
    get:
      tags:
        - 网络信息
      summary: 端口信息查询
      description: 查询端口相关信息
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PortInfo'

  /internalip:
    get:
      tags:
        - 内网管理
      summary: 查询内网IP
      description: 查询内网IP地址
      parameters:
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [GET]
          description: 操作类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InternalIP'
    post:
      tags:
        - 内网管理
      summary: 管理内网IP
      description: 添加、修改、删除内网IP地址
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                op:
                  type: string
                  enum: [ADD, DEL, MOD]
                  description: 操作类型
                id:
                  type: string
                  description: 记录ID
                ip:
                  type: string
                  description: IP地址
                devid:
                  type: integer
                  description: 设备ID
                desc:
                  type: string
                  description: 描述信息
              required:
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /evidence:
    get:
      tags:
        - 证据提取
      summary: 数据包证据提取
      description: 提取网络数据包证据
      parameters:
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: time
          in: query
          schema:
            type: integer
          description: 时间戳（微秒）
        - name: time_sec
          in: query
          schema:
            type: integer
          description: 时间戳（秒）
        - name: time_usec
          in: query
          schema:
            type: integer
          description: 时间戳（微秒部分）
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: download
          in: query
          schema:
            type: boolean
          description: 是否下载
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EvidenceRecord'
            application/octet-stream:
              schema:
                type: string
                format: binary

  /topn:
    get:
      tags:
        - 统计分析
      summary: TopN统计查询
      description: 获取流量TopN统计数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: sortby
          in: query
          schema:
            type: string
          description: 排序字段
        - name: orderby
          in: query
          schema:
            type: string
            enum: [ASC, DESC]
          description: 排序方式
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数
        - name: step
          in: query
          schema:
            type: integer
          description: 时间步长
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: proto
          in: query
          schema:
            type: integer
          description: 协议
        - name: include
          in: query
          schema:
            type: string
          description: 包含条件
        - name: exclude
          in: query
          schema:
            type: string
          description: 排除条件
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TopNRecord'

components:
  securitySchemes:
    SessionAuth:
      type: apiKey
      in: cookie
      name: SESSION_ID
    ApiKeyAuth:
      type: apiKey
      in: query
      name: key
    TokenAuth:
      type: apiKey
      in: query
      name: token

  schemas:
    # 通用响应模式
    SuccessResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success]
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        result:
          type: string
          enum: [failed]
        desc:
          type: string
          description: 错误描述

    # 认证相关
    AuthResponse:
      type: object
      properties:
        code:
          type: integer
          description: 认证状态码
        user_info:
          type: object
          properties:
            username:
              type: string
            role:
              type: string
              enum: [SYSADMIN, ANALYSER, VIEWER]

    LoginResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success]
        session_id:
          type: string
        user_info:
          type: object

    AuthStatusResponse:
      type: object
      properties:
        authenticated:
          type: boolean
        username:
          type: string
        role:
          type: string

    # 流量特征记录
    FeatureRecord:
      type: object
      properties:
        devid:
          type: integer
          description: 设备ID
        type:
          type: string
          description: 特征类型
        time:
          type: integer
          description: 时间戳
        duration:
          type: integer
          description: 持续时间
        protocol:
          type: integer
          description: 协议号
        bytes:
          type: integer
          format: int64
          description: 字节数
        flows:
          type: integer
          format: int64
          description: 流数量
        pkts:
          type: integer
          format: int64
          description: 包数量
        sip:
          type: string
          description: 源IP地址
        sport:
          type: integer
          description: 源端口
        dip:
          type: string
          description: 目标IP地址
        dport:
          type: integer
          description: 目标端口
        peers:
          type: integer
          format: int64
          description: 对端数量
        ip:
          type: string
          description: IP地址
        port:
          type: integer
          description: 端口号
        moid:
          type: integer
          description: 监控对象ID
        qname:
          type: string
          description: 查询域名
        url:
          type: string
          description: URL地址

    # 事件特征记录
    EventFeatureRecord:
      type: object
      properties:
        devid:
          type: integer
          description: 设备ID
        type:
          type: integer
          description: 事件类型
        time:
          type: integer
          description: 时间戳
        protocol:
          type: integer
          description: 协议号
        bytes:
          type: integer
          format: int64
          description: 字节数
        flows:
          type: integer
          format: int64
          description: 流数量
        pkts:
          type: integer
          format: int64
          description: 包数量
        sip:
          type: string
          description: 源IP地址
        sport:
          type: integer
          description: 源端口
        dip:
          type: string
          description: 目标IP地址
        dport:
          type: integer
          description: 目标端口
        domain:
          type: string
          description: 域名
        url:
          type: string
          description: URL地址
        obj:
          type: string
          description: 事件对象

    # 监控对象
    MonitorObject:
      type: object
      properties:
        id:
          type: integer
          description: 监控对象ID
        moip:
          type: string
          description: 监控IP地址
        moport:
          type: string
          description: 监控端口
        protocol:
          type: string
          description: 协议类型
        pip:
          type: string
          description: 对端IP地址
        pport:
          type: string
          description: 对端端口
        desc:
          type: string
          description: 描述信息
        tag:
          type: string
          description: 标签
        group_name:
          type: string
          description: 组名称
        addtime:
          type: string
          description: 添加时间
        filter:
          type: string
          description: 过滤条件
        devid:
          type: integer
          description: 设备ID
        direction:
          type: string
          description: 方向

    # 事件记录
    EventRecord:
      type: object
      properties:
        id:
          type: integer
          description: 事件ID
        time:
          type: integer
          description: 时间戳
        type:
          type: string
          description: 事件类型
        level:
          type: string
          description: 事件级别
        devid:
          type: integer
          description: 设备ID
        obj:
          type: string
          description: 事件对象
        desc:
          type: string
          description: 事件描述
        status:
          type: string
          description: 处理状态

    # 系统控制记录
    SystemControlRecord:
      type: object
      properties:
        id:
          type: integer
          description: 节点ID
        node:
          type: string
          description: 节点类型
        srv:
          type: string
          description: 服务类型
        op:
          type: string
          description: 操作类型
        status:
          type: string
          description: 状态
        result:
          type: string
          description: 操作结果
        desc:
          type: string
          description: 描述信息

    # 配置响应
    ConfigResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success, failed]
        data:
          type: array
          items:
            type: object

    # 黑白名单响应
    BWListResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success]
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              ip:
                type: string
              type:
                type: string
                enum: [black, white]
              desc:
                type: string

    # 位置信息
    LocationInfo:
      type: object
      properties:
        ip:
          type: string
          description: IP地址
        country:
          type: string
          description: 国家
        province:
          type: string
          description: 省份
        city:
          type: string
          description: 城市
        isp:
          type: string
          description: ISP提供商

    # 地理信息
    GeoInfo:
      type: object
      properties:
        ip:
          type: string
          description: IP地址
        country_cn:
          type: string
          description: 国家（中文）
        country_en:
          type: string
          description: 国家（英文）
        province_cn:
          type: string
          description: 省份（中文）
        province_en:
          type: string
          description: 省份（英文）
        city_cn:
          type: string
          description: 城市（中文）
        city_en:
          type: string
          description: 城市（英文）
        isp:
          type: string
          description: ISP提供商

    # 威胁情报
    ThreatInfo:
      type: object
      properties:
        ip:
          type: string
          description: IP地址
        threat_type:
          type: string
          description: 威胁类型
        confidence:
          type: number
          description: 置信度
        last_seen:
          type: string
          description: 最后发现时间
        source:
          type: string
          description: 情报来源

    # 威胁情报专业版
    ThreatInfoPro:
      type: object
      properties:
        ip:
          type: string
          description: IP地址
        threat_types:
          type: array
          items:
            type: string
          description: 威胁类型列表
        risk_score:
          type: integer
          description: 风险评分
        details:
          type: object
          description: 详细信息
        tags:
          type: array
          items:
            type: string
          description: 标签列表

    # IP信息
    IPInfo:
      type: object
      properties:
        ip:
          type: string
          description: IP地址
        type:
          type: string
          description: IP类型
        organization:
          type: string
          description: 组织
        asn:
          type: string
          description: ASN号码
        description:
          type: string
          description: 描述信息

    # 端口信息
    PortInfo:
      type: object
      properties:
        port:
          type: integer
          description: 端口号
        protocol:
          type: string
          description: 协议
        service:
          type: string
          description: 服务名称
        description:
          type: string
          description: 服务描述

    # 内网IP
    InternalIP:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        ip:
          type: string
          description: IP地址
        devid:
          type: integer
          description: 设备ID
        desc:
          type: string
          description: 描述信息
        addtime:
          type: string
          description: 添加时间

    # 证据记录
    EvidenceRecord:
      type: object
      properties:
        devid:
          type: integer
          description: 设备ID
        time_sec:
          type: integer
          description: 时间戳（秒）
        time_usec:
          type: integer
          description: 时间戳（微秒）
        ip:
          type: string
          description: IP地址
        port:
          type: integer
          description: 端口号
        pcap_data:
          type: string
          description: 数据包数据
        file_path:
          type: string
          description: 文件路径

    # TopN记录
    TopNRecord:
      type: object
      properties:
        rank:
          type: integer
          description: 排名
        ip:
          type: string
          description: IP地址
        port:
          type: integer
          description: 端口号
        protocol:
          type: string
          description: 协议
        bytes:
          type: integer
          format: int64
          description: 字节数
        packets:
          type: integer
          format: int64
          description: 包数量
        flows:
          type: integer
          format: int64
          description: 流数量
        peers:
          type: integer
          description: 对端数量
        percentage:
          type: number
          description: 占比百分比

  examples:
    # 流量特征查询示例
    FeatureQueryExample:
      summary: 查询可疑流量特征
      value:
        devid: 1
        starttime: 1640995200
        endtime: 1641081600
        type: "SUS"
        limit: 20

    # 监控对象添加示例
    MonitorObjectAddExample:
      summary: 添加Web服务器监控
      value:
        op: "ADD"
        moip: "*************"
        moport: "80"
        protocol: "TCP"
        desc: "Web服务器监控"
        tag: "production"
        devid: 1

    # 事件查询示例
    EventQueryExample:
      summary: 查询端口扫描事件
      value:
        starttime: 1640995200
        endtime: 1641081600
        type: "PORT_SCAN"
        devid: 1
        level: "HIGH"

    # TopN查询示例
    TopNQueryExample:
      summary: 查询流量TopN
      value:
        devid: 1
        starttime: 1640995200
        endtime: 1641081600
        sortby: "bytes"
        orderby: "DESC"
        limit: 10

tags:
  - name: 认证管理
    description: 用户认证和权限管理相关接口
  - name: 流量分析
    description: 网络流量特征分析和事件特征提取
  - name: 监控管理
    description: 网络监控对象的管理和配置
  - name: 事件管理
    description: 安全事件的查询和管理
  - name: 系统控制
    description: 系统服务的控制和状态查询
  - name: 配置管理
    description: 系统配置的管理和维护
  - name: 地理位置
    description: IP地址地理位置信息查询
  - name: 威胁情报
    description: 威胁情报信息查询和分析
  - name: 网络信息
    description: 网络相关信息查询
  - name: 内网管理
    description: 内网IP地址管理
  - name: 证据提取
    description: 网络数据包证据提取
  - name: 统计分析
    description: 流量统计和TopN分析

# API使用说明
x-api-usage:
  authentication: |
    系统使用基于Session的认证机制：
    1. 首先调用 /login 接口进行登录
    2. 登录成功后会返回SESSION_ID cookie
    3. 后续请求会自动携带cookie进行认证
    4. 部分接口需要特定权限，请确保用户角色正确

  error-handling: |
    所有接口统一返回JSON格式：
    - 成功: {"result": "success", "data": {...}}
    - 失败: {"result": "failed", "desc": "错误描述"}

    HTTP状态码：
    - 200: 请求成功
    - 400: 参数错误
    - 401: 未认证
    - 403: 权限不足
    - 500: 服务器错误

  rate-limiting: |
    系统支持并发请求，但建议：
    - 控制并发数量，避免对系统造成压力
    - 大数据量查询使用合理的时间范围
    - 使用limit参数控制返回记录数

  data-formats: |
    时间格式：Unix时间戳（秒）
    IP地址：支持IPv4和IPv6格式
    端口号：1-65535范围内的整数
    协议：TCP=6, UDP=17, ICMP=1
