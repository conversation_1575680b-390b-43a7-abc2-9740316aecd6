# API接口文档 (Swagger风格)

## 项目信息
- **项目名称：** 高级网络行为分析系统
- **版本：** 1.0.2 开源版
- **基础URL：** http://localhost/d/
- **协议：** HTTP/HTTPS
- **Content-Type：** application/json

---

## 1. 认证模块

### POST /d/login
**用户登录**

**完整URL：** `http://localhost/d/login`

**请求头：**
```
Content-Type: application/json
```

**请求体：**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "string",
    "userInfo": {}
  }
}
```

---

### POST /d/logout
**用户登出**

**完整URL：** `http://localhost/d/logout`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success"
}
```

---

## 2. 事件模块

### POST /d/event
**事件聚合查询**

**完整URL：** `http://localhost/d/event`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "req_type": "aggre",
  "starttime": 1234567890,
  "endtime": 1234567890,
  "limit": 100,
  "offset": 0,
  "filters": {}
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "id": "string",
      "type": "string",
      "level": "string",
      "starttime": 1234567890,
      "endtime": 1234567890,
      "obj": "string"
    }
  ]
}
```

---

### POST /d/event
**事件状态修改**

**完整URL：** `http://localhost/d/event`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "req_type": "set_proc_status",
  "event_id": "string",
  "proc_status": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success"
}
```

---

### POST /d/event
**事件散点图查询**

**完整URL：** `http://localhost/d/event`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "req_type": "scatter",
  "starttime": 1234567890,
  "endtime": 1234567890,
  "obj": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "time": 1234567890,
      "value": 100
    }
  ]
}
```

---

### POST /d/event_feature
**事件特征查询**

**完整URL：** `http://localhost/d/event_feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "starttime": 1234567890,
  "endtime": 1234567890,
  "obj": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "feature_type": "string",
      "feature_data": {}
    }
  ]
}
```

---

### POST /d/evidence
**事件证据查询**

**完整URL：** `http://localhost/d/evidence`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "time": 1234567890123456,
  "devid": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "time_sec": 1234567890,
      "payload": "string",
      "pktlen": 1024,
      "caplen": 1024,
      "protocol": "TCP",
      "sip": "***********",
      "dip": "***********",
      "smac": "00:11:22:33:44:55",
      "dmac": "00:11:22:33:44:66"
    }
  ]
}
```

---

## 3. 资产模块

### POST /d/asset
**通用资产查询**

**完整URL：** `http://localhost/d/asset`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "string",
  "limit": 100,
  "offset": 0,
  "filters": {}
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "asset_id": "string",
      "asset_type": "string",
      "asset_info": {}
    }
  ]
}
```

---

### POST /d/asset
**IP资产查询**

**完整URL：** `http://localhost/d/asset`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "asset_ip",
  "limit": 100,
  "offset": 0,
  "ip": "***********"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "ip": "***********",
      "mac": "00:11:22:33:44:55",
      "hostname": "string",
      "os": "string",
      "location": "string"
    }
  ]
}
```

---

### POST /d/asset
**服务资产查询**

**完整URL：** `http://localhost/d/asset`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "asset_srv",
  "limit": 100,
  "offset": 0,
  "service": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "service_name": "string",
      "port": 80,
      "protocol": "TCP",
      "version": "string",
      "banner": "string"
    }
  ]
}
```

---

### POST /d/asset
**主机资产查询**

**完整URL：** `http://localhost/d/asset`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "asset_host",
  "limit": 100,
  "offset": 0,
  "hostname": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "hostname": "string",
      "ip": "***********",
      "os": "string",
      "services": []
    }
  ]
}
```

---

### POST /d/asset
**URL资产查询**

**完整URL：** `http://localhost/d/asset`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "asset_url",
  "limit": 100,
  "offset": 0,
  "url": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "url": "string",
      "title": "string",
      "status_code": 200,
      "content_type": "text/html"
    }
  ]
}
```

---

### POST /d/statinfo
**资产统计信息**

**完整URL：** `http://localhost/d/statinfo`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "asset",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "total_assets": 1000,
    "ip_count": 500,
    "service_count": 300,
    "host_count": 200
  }
}
```

## 4. 特征模块

### POST /d/feature
**通用特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "string",
  "limit": 100,
  "offset": 0,
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "feature_id": "string",
      "feature_type": "string",
      "feature_data": {}
    }
  ]
}
```

---

### POST /d/feature
**TCP初始化特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "tcpinit",
  "limit": 0,
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "sip": "***********",
      "dip": "***********",
      "sport": 12345,
      "dport": 80,
      "protocol": "TCP",
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**DNS特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "dns",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "query": "example.com",
      "qtype": "A",
      "answer": "***********",
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**扫描特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "scan",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "scanner_ip": "***********",
      "target_ip": "***********",
      "scan_type": "port_scan",
      "ports": [80, 443, 22],
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**可疑特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "sus",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "threat_ip": "***********",
      "victim_ip": "***********",
      "threat_type": "malware",
      "confidence": 0.95,
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**黑名单特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "black",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "blacklist_ip": "***********",
      "blacklist_type": "malicious",
      "source": "threat_intel",
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**服务特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "service",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "service_name": "HTTP",
      "port": 80,
      "protocol": "TCP",
      "banner": "Apache/2.4.41",
      "time": 1234567890
    }
  ]
}
```

---

### POST /d/feature
**监控对象特征查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "mo",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "mo_id": "string",
      "mo_name": "string",
      "mo_type": "string",
      "status": "active",
      "time": 1234567890
    }
  ]
}
```

---

## 5. TopN模块

### POST /d/topn
**TopN统计查询**

**完整URL：** `http://localhost/d/topn`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "type": "string",
  "limit": 10,
  "starttime": 1234567890,
  "endtime": 1234567890,
  "metric": "string"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "rank": 1,
      "item": "string",
      "value": 1000,
      "percentage": 25.5
    }
  ]
}
```

---

## 6. 配置模块

### POST /d/config
**设备配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "agent",
  "target": "device"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "device_id": "string",
      "device_name": "string",
      "device_ip": "***********",
      "device_type": "sensor",
      "status": "online"
    }
  ]
}
```

---

### POST /d/config
**代理配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "agent"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "proxy_id": "string",
      "proxy_name": "string",
      "proxy_ip": "***********",
      "proxy_port": 8080,
      "status": "active"
    }
  ]
}
```

---

### POST /d/config
**用户配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "user"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "user_id": "string",
      "username": "string",
      "role": "admin",
      "permissions": [],
      "status": "active"
    }
  ]
}
```

---

### POST /d/config
**监控对象配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "mo"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "mo_id": "string",
      "mo_name": "string",
      "mo_type": "ip_range",
      "mo_value": "***********/24",
      "description": "string"
    }
  ]
}
```

---

### POST /d/config
**监控对象组配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "gget",
  "type": "mo_group"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "group_id": "string",
      "group_name": "string",
      "mo_list": [],
      "description": "string"
    }
  ]
}
```

---

### POST /d/config
**内网IP配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "internalip"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "ip_range": "***********/24",
      "description": "Internal network",
      "status": "active"
    }
  ]
}
```

---

### POST /d/config
**黑名单配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "bwlist",
  "target": "blacklist"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "item_id": "string",
      "item_type": "ip",
      "item_value": "***********",
      "reason": "malicious",
      "create_time": 1234567890
    }
  ]
}
```

---

### POST /d/config
**白名单配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "bwlist",
  "target": "whitelist"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "item_id": "string",
      "item_type": "ip",
      "item_value": "***********",
      "reason": "trusted",
      "create_time": 1234567890
    }
  ]
}
```

---

## 7. 事件配置模块

### POST /d/config
**事件基础配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "event_id": "string",
      "event_name": "string",
      "event_type": "string",
      "enabled": true,
      "config": {}
    }
  ]
}
```

---

### POST /d/config
**事件类型配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event_type"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "type_id": "string",
      "type_name": "string",
      "description": "string",
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**事件级别配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event_level"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "level_id": "string",
      "level_name": "string",
      "level_value": 1,
      "color": "#ff0000"
    }
  ]
}
```

---

### POST /d/config
**事件详细配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "config_id": "string",
      "event_type": "string",
      "config_data": {},
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**监控对象事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "threshold",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "threshold_id": "string",
      "mo_id": "string",
      "threshold_value": 1000,
      "threshold_type": "bytes",
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**扫描事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "scan",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "scan_config_id": "string",
      "scan_type": "port_scan",
      "detection_rules": {},
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**DoS事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "srv",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "dos_config_id": "string",
      "service_type": "string",
      "threshold": 1000,
      "time_window": 60,
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**可疑事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "sus",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "sus_config_id": "string",
      "detection_method": "ml",
      "confidence_threshold": 0.8,
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**黑名单事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "black",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "black_config_id": "string",
      "blacklist_sources": [],
      "auto_update": true,
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**DNS事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "dns",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "dns_config_id": "string",
      "monitor_types": ["A", "AAAA", "CNAME"],
      "suspicious_domains": [],
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**DNS隧道事件配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "event_type": "dns_tun",
  "type": "event_config"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "dnstun_config_id": "string",
      "detection_algorithms": [],
      "threshold_params": {},
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**事件动作配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event_action"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "action_id": "string",
      "action_name": "string",
      "action_type": "alert",
      "parameters": {},
      "enabled": true
    }
  ]
}
```

---

### POST /d/config
**事件忽略配置查询**

**完整URL：** `http://localhost/d/config`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "get",
  "type": "event_ignore"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "ignore_id": "string",
      "ignore_type": "ip",
      "ignore_value": "***********",
      "reason": "false_positive",
      "enabled": true
    }
  ]
}
```

---

## 8. 工具API模块

### POST /d/geoinfo
**IP地理位置信息查询**

**完整URL：** `http://localhost/d/geoinfo`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "ipList": ["***********", "********"]
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "ip": "***********",
      "country": "China",
      "province": "Beijing",
      "city": "Beijing",
      "isp": "China Telecom",
      "latitude": 39.9042,
      "longitude": 116.4074
    }
  ]
}
```

---

### POST /d/portinfo
**端口服务信息查询**

**完整URL：** `http://localhost/d/portinfo`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "portlist": [80, 443, 22, 3389]
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "port": 80,
      "protocol": "TCP",
      "service": "HTTP",
      "description": "World Wide Web HTTP"
    }
  ]
}
```

---

### POST /d/ipinfo
**IP详细信息查询**

**完整URL：** `http://localhost/d/ipinfo`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "iplist": "***********,********"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "ip": "***********",
      "hostname": "server01.local",
      "mac": "00:11:22:33:44:55",
      "os": "Windows Server 2019",
      "services": [],
      "last_seen": 1234567890
    }
  ]
}
```

---

### POST /d/threatinfo
**威胁情报查询**

**完整URL：** `http://localhost/d/threatinfo`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "key": "***********",
  "op": "get"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "ip": "***********",
    "threat_type": "malware",
    "confidence": 0.95,
    "source": "threat_intel_feed",
    "first_seen": 1234567890,
    "last_seen": 1234567890,
    "tags": ["botnet", "c2"]
  }
}
```

**特殊说明：** 超时时间30秒

---

### POST /d/threatinfopro
**威胁情报专业版查询**

**完整URL：** `http://localhost/d/threatinfopro`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "list": ["***********", "example.com"],
  "type": "ip_domain"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "indicator": "***********",
      "type": "ip",
      "threat_types": ["malware", "botnet"],
      "confidence": 0.95,
      "sources": ["feed1", "feed2"],
      "attributes": {}
    }
  ]
}
```

---

## 9. 系统控制模块

### POST /d/sctl
**系统状态查询**

**完整URL：** `http://localhost/d/sctl`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "system_status": "running",
    "services": [
      {
        "service_name": "analyzer",
        "status": "running",
        "pid": 1234,
        "uptime": 3600
      }
    ],
    "system_info": {
      "cpu_usage": 25.5,
      "memory_usage": 60.2,
      "disk_usage": 45.8
    }
  }
}
```

---

### POST /d/sctl
**系统服务启动**

**完整URL：** `http://localhost/d/sctl`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "start",
  "service": "analyzer"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Service started successfully",
  "data": {
    "service": "analyzer",
    "status": "running",
    "pid": 1234
  }
}
```

---

### POST /d/sctl
**系统服务停止**

**完整URL：** `http://localhost/d/sctl`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "stop",
  "service": "analyzer"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Service stopped successfully",
  "data": {
    "service": "analyzer",
    "status": "stopped"
  }
}
```

---

### POST /d/sctl
**系统服务重启**

**完整URL：** `http://localhost/d/sctl`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "op": "restart",
  "service": "analyzer"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "Service restarted successfully",
  "data": {
    "service": "analyzer",
    "status": "running",
    "pid": 5678
  }
}
```

---

## 10. 可疑信息模块

### POST /d/feature
**可疑连接信息查询**

**完整URL：** `http://localhost/d/feature`

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体：**
```json
{
  "limit": 0,
  "type": "sus",
  "ti_mark": "res",
  "starttime": 1234567890,
  "endtime": 1234567890
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "threat_ip": "***********",
      "victim_ip": "***********",
      "threat_type": "malware",
      "confidence": 0.95,
      "connection_count": 100,
      "bytes_transferred": 1024000,
      "time": 1234567890
    }
  ]
}
```

**特殊说明：** endtime参数会自动加300秒以适配数据规则

---

## 前端路由配置

### 主要页面路由
- **GET** `/login` - 登录页面
- **GET** `/overview` - 总览页面
  - **GET** `/overview/om` - 运维总览
  - **GET** `/overview/an` - 分析总览
  - **GET** `/overview/ma` - 管理总览
- **GET** `/event/list` - 事件列表
- **GET** `/event/detail` - 事件详情
- **GET** `/search` - 搜索页面
- **GET** `/result` - 搜索结果页面
- **GET** `/track` - 追踪页面
- **GET** `/config` - 配置页面
  - 事件配置
  - 黑白名单配置
  - 资产配置
  - 系统配置
  - 监控对象配置
  - 目录配置

---

## API接口汇总表

| 序号 | 接口名称 | 请求方法 | 完整URL | 功能描述 |
|------|----------|----------|---------|----------|
| 1 | login | POST | http://localhost/d/login | 用户登录认证 |
| 2 | logout | POST | http://localhost/d/logout | 用户登出 |
| 3 | eventGet | POST | http://localhost/d/event | 事件聚合查询 |
| 4 | eventStatusMod | POST | http://localhost/d/event | 事件状态修改 |
| 5 | eventInfoGet | POST | http://localhost/d/event | 事件散点图查询 |
| 6 | eventFeature | POST | http://localhost/d/event_feature | 事件特征查询 |
| 7 | eventEvidence | POST | http://localhost/d/evidence | 事件证据查询 |
| 8 | assetGet | POST | http://localhost/d/asset | 通用资产查询 |
| 9 | assetIp | POST | http://localhost/d/asset | IP资产查询 |
| 10 | assetSrv | POST | http://localhost/d/asset | 服务资产查询 |
| 11 | assetHost | POST | http://localhost/d/asset | 主机资产查询 |
| 12 | assetUrl | POST | http://localhost/d/asset | URL资产查询 |
| 13 | statinfoGet | POST | http://localhost/d/statinfo | 资产统计信息 |
| 14 | featureGet | POST | http://localhost/d/feature | 通用特征查询 |
| 15 | featureTcpinit | POST | http://localhost/d/feature | TCP初始化特征 |
| 16 | featureDns | POST | http://localhost/d/feature | DNS特征查询 |
| 17 | featureScan | POST | http://localhost/d/feature | 扫描特征查询 |
| 18 | featureSus | POST | http://localhost/d/feature | 可疑特征查询 |
| 19 | featureBlack | POST | http://localhost/d/feature | 黑名单特征查询 |
| 20 | featureService | POST | http://localhost/d/feature | 服务特征查询 |
| 21 | featureMo | POST | http://localhost/d/feature | 监控对象特征查询 |
| 22 | topnGet | POST | http://localhost/d/topn | TopN统计查询 |
| 23 | deviceApi | POST | http://localhost/d/config | 设备配置查询 |
| 24 | proxyApi | POST | http://localhost/d/config | 代理配置查询 |
| 25 | userApi | POST | http://localhost/d/config | 用户配置查询 |
| 26 | moApi | POST | http://localhost/d/config | 监控对象配置查询 |
| 27 | mogroupApi | POST | http://localhost/d/config | 监控对象组配置查询 |
| 28 | internalApi | POST | http://localhost/d/config | 内网IP配置查询 |
| 29 | blacklistApi | POST | http://localhost/d/config | 黑名单配置查询 |
| 30 | whitelistApi | POST | http://localhost/d/config | 白名单配置查询 |
| 31 | eventConfigApiConfig | POST | http://localhost/d/config | 事件基础配置查询 |
| 32 | eventConfigApiType | POST | http://localhost/d/config | 事件类型配置查询 |
| 33 | eventConfigApiLevel | POST | http://localhost/d/config | 事件级别配置查询 |
| 34 | eventConfigApi | POST | http://localhost/d/config | 事件详细配置查询 |
| 35 | eventConfigApiMo | POST | http://localhost/d/config | 监控对象事件配置查询 |
| 36 | eventConfigApiScan | POST | http://localhost/d/config | 扫描事件配置查询 |
| 37 | eventConfigApiDos | POST | http://localhost/d/config | DoS事件配置查询 |
| 38 | eventConfigApiSus | POST | http://localhost/d/config | 可疑事件配置查询 |
| 39 | eventConfigApiBlack | POST | http://localhost/d/config | 黑名单事件配置查询 |
| 40 | eventConfigApiDns | POST | http://localhost/d/config | DNS事件配置查询 |
| 41 | eventConfigApiDnstun | POST | http://localhost/d/config | DNS隧道事件配置查询 |
| 42 | eventConfigApiAction | POST | http://localhost/d/config | 事件动作配置查询 |
| 43 | eventConfigApiIgnore | POST | http://localhost/d/config | 事件忽略配置查询 |
| 44 | geoinfo | POST | http://localhost/d/geoinfo | IP地理位置信息查询 |
| 45 | portinfo | POST | http://localhost/d/portinfo | 端口服务信息查询 |
| 46 | ipInfo | POST | http://localhost/d/ipinfo | IP详细信息查询 |
| 47 | threatinfo | POST | http://localhost/d/threatinfo | 威胁情报查询 |
| 48 | threatinfoPro | POST | http://localhost/d/threatinfopro | 威胁情报专业版查询 |
| 49 | sctlStat | POST | http://localhost/d/sctl | 系统状态查询 |
| 50 | sctlStart | POST | http://localhost/d/sctl | 系统服务启动 |
| 51 | sctlStop | POST | http://localhost/d/sctl | 系统服务停止 |
| 52 | sctlRestart | POST | http://localhost/d/sctl | 系统服务重启 |
| 53 | susInfoGet | POST | http://localhost/d/feature | 可疑连接信息查询 |

---

## 特殊说明

### 认证机制
- 除登录接口外，所有接口都需要在请求头中携带 `Authorization: Bearer {token}`
- Token通过登录接口获取

### 缓存机制
- 系统支持请求缓存，可通过 `window.appConfig.isCacheRequset` 控制
- 缓存的接口包括：asset、evidence
- 缓存键格式：`{url}^{params}`

### 超时设置
- 威胁情报接口(threatinfo)超时时间为30秒
- 其他接口使用默认超时时间

### 请求取消
- 支持请求取消功能，特别是以下接口：
  - portinfo
  - geoinfo
  - threatinfo
  - feature

### 错误处理
- 统一错误处理机制
- 操作失败时返回 `[{failed}]` 标识
- HTTP状态码遵循RESTful规范

### 事件过滤
- 支持忽略指定IP的事件，通过 `window.appConfig.ignoreEventIpArr` 配置
- 默认忽略：['0.0.0.0', '***************']
- 过滤开关：`window.appConfig.ignoreEventSwitch`

### 数据格式
- 所有接口统一使用JSON格式
- 时间戳使用Unix时间戳（秒级）
- 微秒级时间戳用于精确的数据包时间

---

## 配置参数

### 全局配置 (window.appConfig)
```javascript
{
  baseUrl: '/d/',                    // API基础路径
  version: '1.0.2',                  // 系统版本
  subName: '开源版',                  // 版本标识
  eventFeatureLimit: 100,            // 事件Feature接口条数限制
  isCacheRequset: true,              // 缓存开关
  ignoreEventSwitch: true,           // 事件忽略开关
  ignoreEventIpArr: [                // 忽略的IP列表
    '0.0.0.0',
    '***************'
  ]
}
```

### 环境变量
- `REACT_APP_ENV`: 环境标识，mock模式下会使用模拟数据

---

## 开发说明

### 技术栈
- **前端框架：** React 18+
- **UI组件库：** Ant Design 4+
- **状态管理：** MobX
- **HTTP客户端：** Axios
- **路由管理：** React Router
- **构建工具：** Create React App

### 项目结构
```
packages/
├── std/                    # 主应用
│   ├── src/
│   │   ├── service/       # API服务层
│   │   ├── page/          # 页面组件
│   │   ├── layout/        # 布局组件
│   │   └── config/        # 配置文件
└── components/            # 共享组件库
    ├── ui/               # UI组件
    ├── utils/            # 工具函数
    └── system/           # 系统组件
```

---
